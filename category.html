<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Horizontal Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            min-height: 100vh;
            overflow-x: hidden;
            color: white;
        }

        .container {
            padding: 2rem 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }

        .cards-container {
            display: flex;
            gap: 2rem;
            padding: 0 2rem;
            overflow-x: auto;
            overflow-y: visible;
            height: 400px;
            align-items: center;
            scrollbar-width: thin;
            scrollbar-color: #4ecdc4 transparent;
        }

        .cards-container::-webkit-scrollbar {
            height: 8px;
        }

        .cards-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .cards-container::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 10px;
        }

        .card {
            min-width: 280px;
            height: 180px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(78, 205, 196, 0.3);
        }

        .card.expanded {
            min-width: 400px;
            height: 300px;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(78, 205, 196, 0.5);
            z-index: 10;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
        }

        .card-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 1rem;
        }

        .card-content {
            font-size: 0.9rem;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.8);
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card.expanded .card-content {
            opacity: 1;
            max-height: 200px;
        }

        .hover-image {
            position: fixed;
            width: 200px;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            transform: translateY(-50%);
            overflow: hidden;
        }

        .hover-image.visible {
            opacity: 1;
        }

        .hover-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .hover-image.visible::before {
            left: 100%;
        }

        /* Mobile and tablet responsiveness */
        @media (max-width: 1024px) {
            .container {
                padding: 1rem 0;
            }
            
            .title {
                font-size: 2rem;
                margin-bottom: 1.5rem;
                padding: 0 1rem;
            }
            
            .cards-container {
                padding: 0 1rem;
                height: auto;
                min-height: 350px;
            }
            
            .card {
                min-width: 260px;
                height: 160px;
                padding: 1.2rem;
            }
            
            .card.expanded {
                min-width: 320px;
                height: 280px;
            }
        }

        @media (max-width: 768px) {
            .hover-image {
                display: none !important;
            }
            
            .title {
                font-size: 1.8rem;
                margin-bottom: 1rem;
            }
            
            .cards-container {
                gap: 1.5rem;
                padding: 0 1rem;
                height: auto;
                min-height: 300px;
            }
            
            .card {
                min-width: 240px;
                height: 140px;
                padding: 1rem;
            }
            
            .card.expanded {
                min-width: 280px;
                height: 260px;
            }
            
            .card-title {
                font-size: 1.1rem;
            }
            
            .card-subtitle {
                font-size: 0.85rem;
            }
            
            .card-content {
                font-size: 0.85rem;
            }
            
            .card-icon {
                width: 35px;
                height: 35px;
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0.5rem 0;
            }
            
            .title {
                font-size: 1.6rem;
                margin-bottom: 1rem;
            }
            
            .cards-container {
                gap: 1rem;
                padding: 0 0.5rem;
                min-height: 280px;
            }
            
            .card {
                min-width: 220px;
                height: 130px;
                padding: 0.8rem;
            }
            
            .card.expanded {
                min-width: 260px;
                height: 240px;
            }
            
            .card-title {
                font-size: 1rem;
            }
            
            .card-subtitle {
                font-size: 0.8rem;
                margin-bottom: 0.8rem;
            }
            
            .card-content {
                font-size: 0.8rem;
                line-height: 1.4;
            }
            
            .card-icon {
                width: 30px;
                height: 30px;
                font-size: 1rem;
            }
            
            .expand-hint {
                bottom: 8px;
                right: 12px;
                font-size: 0.7rem;
            }
        }

        .expand-hint {
            position: absolute;
            bottom: 10px;
            right: 15px;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .card.expanded .expand-hint::after {
            content: " (click to collapse)";
        }

        .card:not(.expanded) .expand-hint::after {
            content: " (click to expand)";
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Interactive Card Collection</h1>
        <div class="cards-container" id="cardsContainer">
            <!-- Cards will be generated by JavaScript -->
        </div>
    </div>

    <div class="hover-image" id="hoverImage">
        <span id="hoverEmoji">🎨</span>
    </div>

    <script>
        const cardsData = [
            {
                icon: "🎨",
                title: "Creative Design",
                subtitle: "Unleash your creativity",
                content: "Dive deep into the world of creative design with modern tools and techniques. Learn about color theory, typography, composition, and user experience design. Master the art of visual storytelling and create stunning designs that captivate your audience.",
                hoverEmoji: "🎨"
            },
            {
                icon: "🚀",
                title: "Space Exploration",
                subtitle: "Journey to the stars",
                content: "Explore the vast cosmos and learn about the latest developments in space technology. From Mars missions to the search for extraterrestrial life, discover the wonders of the universe and humanity's quest to explore beyond our planet.",
                hoverEmoji: "🛸"
            },
            {
                icon: "🧬",
                title: "Biotechnology",
                subtitle: "The future of science",
                content: "Delve into the revolutionary world of biotechnology where science meets innovation. Learn about gene editing, synthetic biology, and how these technologies are reshaping medicine, agriculture, and our understanding of life itself.",
                hoverEmoji: "🔬"
            },
            {
                icon: "🤖",
                title: "Artificial Intelligence",
                subtitle: "Machine learning revolution",
                content: "Understand the fundamentals of AI and machine learning. Explore neural networks, deep learning algorithms, and how artificial intelligence is transforming industries from healthcare to autonomous vehicles and beyond.",
                hoverEmoji: "🧠"
            },
            {
                icon: "🌍",
                title: "Sustainability",
                subtitle: "Protecting our planet",
                content: "Learn about sustainable practices and environmental conservation. Discover renewable energy solutions, circular economy principles, and how individuals and organizations can contribute to a more sustainable future for our planet.",
                hoverEmoji: "♻️"
            },
            {
                icon: "💎",
                title: "Blockchain Tech",
                subtitle: "Decentralized future",
                content: "Explore the revolutionary potential of blockchain technology beyond cryptocurrency. Learn about smart contracts, decentralized applications, and how blockchain is creating new possibilities for transparency and security.",
                hoverEmoji: "⛓️"
            },
            {
                icon: "🎵",
                title: "Music Production",
                subtitle: "Create amazing sounds",
                content: "Master the art of music production with digital audio workstations, synthesizers, and mixing techniques. Learn about sound design, music theory, and how to create professional-quality tracks from your home studio.",
                hoverEmoji: "🎧"
            },
            {
                icon: "📱",
                title: "Mobile Development",
                subtitle: "Apps that change lives",
                content: "Build powerful mobile applications for iOS and Android. Learn about cross-platform development, user interface design, and how to create apps that solve real-world problems and enhance user experiences.",
                hoverEmoji: "📲"
            }
        ];

        const cardsContainer = document.getElementById('cardsContainer');
        const hoverImage = document.getElementById('hoverImage');
        const hoverEmoji = document.getElementById('hoverEmoji');

        let currentExpandedCard = null;

        // Check if device supports hover (desktop)
        const supportsHover = window.matchMedia('(hover: hover)').matches;

        // Generate cards
        cardsData.forEach((cardData, index) => {
            const card = document.createElement('div');
            card.className = 'card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="card-icon">${cardData.icon}</div>
                    <div>
                        <div class="card-title">${cardData.title}</div>
                    </div>
                </div>
                <div class="card-subtitle">${cardData.subtitle}</div>
                <div class="card-content">${cardData.content}</div>
                <div class="expand-hint">💫</div>
            `;

            // Only add hover effects on devices that support hover (desktop)
            if (supportsHover && window.innerWidth > 768) {
                // Mouse enter event
                card.addEventListener('mouseenter', (e) => {
                    // Don't show hover image if card is expanded
                    if (!card.classList.contains('expanded')) {
                        hoverEmoji.textContent = cardData.hoverEmoji;
                        hoverImage.classList.add('visible');
                    }
                });

                // Mouse move event
                card.addEventListener('mousemove', (e) => {
                    // Only move image if card is not expanded
                    if (!card.classList.contains('expanded')) {
                        hoverImage.style.left = (e.clientX + 20) + 'px';
                        hoverImage.style.top = e.clientY + 'px';
                    }
                });

                // Mouse leave event
                card.addEventListener('mouseleave', () => {
                    hoverImage.classList.remove('visible');
                });
            }

            // Click event for expansion
            card.addEventListener('click', (e) => {
                e.stopPropagation();
                
                if (currentExpandedCard && currentExpandedCard !== card) {
                    currentExpandedCard.classList.remove('expanded');
                }

                if (card.classList.contains('expanded')) {
                    card.classList.remove('expanded');
                    currentExpandedCard = null;
                } else {
                    card.classList.add('expanded');
                    currentExpandedCard = card;
                    
                    // Hide hover image when card expands
                    hoverImage.classList.remove('visible');
                    
                    // Smooth scroll to show the expanded card
                    card.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'center'
                    });
                }
            });

            cardsContainer.appendChild(card);
        });

        // Click outside to collapse
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.card') && currentExpandedCard) {
                currentExpandedCard.classList.remove('expanded');
                currentExpandedCard = null;
            }
        });

        // Smooth horizontal scrolling with mouse wheel
        cardsContainer.addEventListener('wheel', (e) => {
            if (e.deltaY !== 0) {
                e.preventDefault();
                cardsContainer.scrollLeft += e.deltaY;
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Image Slider</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 300vh;
            overflow-x: hidden;
        }

        .container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            perspective: 1000px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slider-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
        }

        .image-card {
            position: absolute;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform-style: preserve-3d;
            cursor: pointer;
        }

        .image-card:nth-child(1) { width: 180px; height: 240px; }
        .image-card:nth-child(2) { width: 220px; height: 160px; }
        .image-card:nth-child(3) { width: 160px; height: 200px; }
        .image-card:nth-child(4) { width: 200px; height: 280px; }
        .image-card:nth-child(5) { width: 240px; height: 180px; }
        .image-card:nth-child(6) { width: 190px; height: 220px; }

        .image-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            z-index: 1;
            border-radius: 15px;
        }

        .image-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.6s ease;
        }

        .image-card:hover {
            transform: scale(1.05);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
        }

        .image-card:hover img {
            transform: scale(1.1);
        }

        .scroll-indicator {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            animation: bounce 2s infinite;
            z-index: 100;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
            40% { transform: translateX(-50%) translateY(-10px); }
            60% { transform: translateX(-50%) translateY(-5px); }
        }

        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    
    <div class="container">
        <div class="slider-wrapper" id="sliderWrapper">
            <div class="image-card" data-index="0">
                <img src="https://images.unsplash.com/photo-1743397015920-e4682a813b24?w=400&h=600&fit=crop" alt="Shop">
            </div>
            <div class="image-card" data-index="1">
                <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop" alt="Fantasy Art 2">
            </div>
            <div class="image-card" data-index="2">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop" alt="Space Art 3">
            </div>
            <div class="image-card" data-index="3">
                <img src="https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=400&h=600&fit=crop" alt="Abstract Art 4">
            </div>
            <div class="image-card" data-index="4">
                <img src="https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=400&h=600&fit=crop" alt="Digital Art 5">
            </div>
            <div class="image-card" data-index="5">
                <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=600&fit=crop" alt="Cosmic Art 6">
            </div>
        </div>
    </div>

    <div class="scroll-indicator">
        <div>↓ Scroll to explore ↓</div>
    </div>

    <script>
        const cards = document.querySelectorAll('.image-card');
        const sliderWrapper = document.getElementById('sliderWrapper');
        let scrollY = 0;
        let currentScroll = 0;
        let targetScroll = 0;
        
        // Create floating particles
        function createParticles() {
            const particleContainer = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particleContainer.appendChild(particle);
            }
        }

        // Position cards in 3D space
        function positionCards() {
            const centerX = window.innerWidth / 2;
            const centerY = window.innerHeight / 2;
            const radius = 400;
            
            cards.forEach((card, index) => {
                const angle = (index / cards.length) * Math.PI * 2;
                const x = Math.cos(angle + currentScroll * 0.01) * radius;
                const z = Math.sin(angle + currentScroll * 0.01) * radius;
                const y = Math.sin(currentScroll * 0.005 + index) * 100;
                
                // No rotation - cards always face forward
                card.style.transform = `
                    translate3d(${centerX + x - 150}px, ${centerY + y - 200}px, ${z}px)
                `;
                
                // Adjust opacity based on z-position
                const opacity = Math.max(0.2, (z + radius) / (radius * 2));
                card.style.opacity = opacity;
                
                // Scale based on distance
                const scale = 0.6 + (opacity * 0.4);
                card.style.filter = `blur(${(1 - opacity) * 3}px)`;
            });
        }

        // Smooth scroll animation
        function animate() {
            targetScroll = window.pageYOffset;
            currentScroll += (targetScroll - currentScroll) * 0.05;
            
            positionCards();
            requestAnimationFrame(animate);
        }

        // Handle scroll
        function handleScroll() {
            scrollY = window.pageYOffset;
        }

        // Add click interaction
        cards.forEach((card, index) => {
            card.addEventListener('click', () => {
                card.style.transform += ' scale(1.2)';
                setTimeout(() => {
                    positionCards();
                }, 300);
            });
        });

        // Initialize
        window.addEventListener('scroll', handleScroll);
        window.addEventListener('resize', positionCards);
        
        createParticles();
        positionCards();
        animate();

        // Add some initial animation delay
        cards.forEach((card, index) => {
            card.style.animationDelay = (index * 0.1) + 's';
        });
    </script>
</body>
</html>